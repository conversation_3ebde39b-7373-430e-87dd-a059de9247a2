// SEO Configuration for Apex Solutions

export const seoConfig = {
  // Site Information
  siteName: process.env.NEXT_PUBLIC_COMPANY_NAME || 'Apex Solutions',
  siteUrl: process.env.NEXT_PUBLIC_URL || 'https://www.apex-solutions.com',
  siteDescription: 'Professional digital solutions: Website creation, SEO, content writing, graphics, video editing, transcription, and data analysis services for businesses worldwide.',
  
  // Default Meta
  defaultTitle: `${process.env.NEXT_PUBLIC_COMPANY_NAME || 'Apex Solutions'} - Professional Digital Solutions`,
  titleTemplate: `%s | ${process.env.NEXT_PUBLIC_COMPANY_NAME || 'Apex Solutions'}`,
  
  // Keywords
  keywords: [
    'professional digital solutions',
    'website creation',
    'SEO services',
    'professional writing',
    'content writing',
    'graphics design',
    'video editing',
    'transcription services',
    'data entry',
    'data analysis',
    'freelancing services',
    'business solutions',
    'digital marketing',
    'web development',
    'Kenya',
    'USA',
    'Nairobi',
    'California',
    process.env.NEXT_PUBLIC_COMPANY_NAME || 'Apex Solutions',
    process.env.NEXT_PUBLIC_COMPANY_ALTERNATE_NAME || 'Collo-Script'
  ],
  
  // Social Media
  social: {
    twitter: process.env.NEXT_PUBLIC_TWITTER_HANDLE || '@apexsolutions',
    facebook: process.env.NEXT_PUBLIC_FACEBOOK_URL || 'https://facebook.com/apexsolutions',
    linkedin: process.env.NEXT_PUBLIC_LINKEDIN_URL || 'https://linkedin.com/company/apexsolutions',
    instagram: process.env.NEXT_PUBLIC_INSTAGRAM_URL || 'https://instagram.com/apexsolutions',
  },
  
  // Contact Information
  contact: {
    email: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
    phone: {
      usa: process.env.NEXT_PUBLIC_PHONE_USA || '******-456-77',
      kenya: process.env.NEXT_PUBLIC_PHONE_KENYA || '+***********-383'
    },
    address: {
      usa: {
        street: '254 654, GFTH, LA',
        city: 'California',
        country: 'US'
      },
      kenya: {
        street: 'Vision Plaza, Mombasa Rd.',
        city: 'Nairobi',
        country: 'KE'
      }
    }
  },
  
  // Business Information
  business: {
    name: process.env.NEXT_PUBLIC_COMPANY_NAME || 'Apex Solutions',
    alternateName: process.env.NEXT_PUBLIC_COMPANY_ALTERNATE_NAME || 'Collo-Script',
    foundingDate: process.env.NEXT_PUBLIC_FOUNDING_YEAR || '2024',
    priceRange: '$',
    rating: {
      value: '4.9',
      count: '127'
    },
    services: [
      'Website Development',
      'SEO Services',
      'Content Writing',
      'Graphics Design',
      'Video Editing',
      'Transcription Services',
      'Data Entry',
      'Data Analysis'
    ]
  },
  
  // Images
  images: {
    logo: '/opengraph-image.png',
    ogImage: '/opengraph-image.png',
    favicon: '/favicon.ico'
  },
  
  // Analytics & Verification
  analytics: {
    googleAnalytics: process.env.NEXT_PUBLIC_GA_ID || 'G-XXXXXXXXXX',
    googleTagManager: process.env.NEXT_PUBLIC_GTM_ID || 'GTM-XXXXXXX',
    microsoftClarity: process.env.NEXT_PUBLIC_CLARITY_ID || 'XXXXXXXXX',
  },
  
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION || 'your-google-verification-code',
    bing: process.env.NEXT_PUBLIC_BING_VERIFICATION || 'your-bing-verification-code',
    yandex: process.env.NEXT_PUBLIC_YANDEX_VERIFICATION || 'your-yandex-verification-code',
    yahoo: process.env.NEXT_PUBLIC_YAHOO_VERIFICATION || 'your-yahoo-verification-code'
  }
};

// Page-specific SEO configurations
export const pageConfigs = {
  home: {
    title: 'Professional Digital Solutions for Your Business',
    description: 'Transform your business with our comprehensive digital solutions. Website creation, SEO, content writing, graphics, video editing, transcription, and data analysis services.',
    keywords: ['digital solutions', 'business services', 'website creation', 'SEO', 'content writing']
  },
  
  websiteCreation: {
    title: 'Professional Website Creation & SEO Services',
    description: 'Custom website development with SEO optimization. Responsive designs, fast loading, and search engine friendly websites for businesses.',
    keywords: ['website creation', 'web development', 'SEO services', 'responsive design', 'custom websites']
  },
  
  writing: {
    title: 'Professional Writing & Content Creation Services',
    description: 'Expert writing services including content writing, copywriting, editing, proofreading, research papers, and ghostwriting for businesses.',
    keywords: ['professional writing', 'content writing', 'copywriting', 'editing', 'proofreading', 'ghostwriting']
  },
  
  graphics: {
    title: 'Graphics Design & Video Editing Services',
    description: 'Creative graphics design and professional video editing services. Logos, brochures, social media graphics, and video content creation.',
    keywords: ['graphics design', 'video editing', 'logo design', 'brochures', 'social media graphics']
  },
  
  transcription: {
    title: 'Professional Transcription Services',
    description: 'Accurate audio and video transcription services with fast turnaround times. Medical, legal, academic, and business transcription.',
    keywords: ['transcription services', 'audio transcription', 'video transcription', 'medical transcription']
  },
  
  dataEntry: {
    title: 'Data Entry & Analysis Services',
    description: 'Professional data entry, data mining, data cleaning, and analysis services. Transform raw data into actionable insights.',
    keywords: ['data entry', 'data analysis', 'data mining', 'data cleaning', 'data processing']
  }
};

// Generate structured data for different page types
type ServiceData = {
  name?: string;
  description?: string;
  serviceType?: string[] | string;
};

export const generateStructuredData = (
  type: 'organization' | 'localBusiness' | 'service',
  data?: ServiceData
) => {
  const baseUrl = seoConfig.siteUrl;
  
  switch (type) {
    case 'organization':
      return {
        "@context": "https://schema.org",
        "@type": "Organization",
        "name": seoConfig.business.name,
        "alternateName": seoConfig.business.alternateName,
        "url": baseUrl,
        "logo": `${baseUrl}${seoConfig.images.logo}`,
        "description": seoConfig.siteDescription,
        "foundingDate": seoConfig.business.foundingDate,
        "contactPoint": [
          {
            "@type": "ContactPoint",
            "telephone": seoConfig.contact.phone.usa,
            "contactType": "customer service",
            "areaServed": "US",
            "availableLanguage": "English"
          },
          {
            "@type": "ContactPoint",
            "telephone": seoConfig.contact.phone.kenya,
            "contactType": "customer service",
            "areaServed": "KE",
            "availableLanguage": "English"
          }
        ],
        "address": [
          {
            "@type": "PostalAddress",
            "streetAddress": seoConfig.contact.address.usa.street,
            "addressLocality": seoConfig.contact.address.usa.city,
            "addressCountry": seoConfig.contact.address.usa.country
          },
          {
            "@type": "PostalAddress",
            "streetAddress": seoConfig.contact.address.kenya.street,
            "addressLocality": seoConfig.contact.address.kenya.city,
            "addressCountry": seoConfig.contact.address.kenya.country
          }
        ],
        "sameAs": [
          seoConfig.social.facebook,
          seoConfig.social.linkedin,
          seoConfig.social.instagram
        ],
        "serviceType": seoConfig.business.services
      };
      
    case 'localBusiness':
      return {
        "@context": "https://schema.org",
        "@type": "LocalBusiness",
        "name": seoConfig.business.name,
        "image": `${baseUrl}${seoConfig.images.logo}`,
        "description": seoConfig.siteDescription,
        "url": baseUrl,
        "telephone": seoConfig.contact.phone.kenya,
        "address": {
          "@type": "PostalAddress",
          "streetAddress": seoConfig.contact.address.kenya.street,
          "addressLocality": seoConfig.contact.address.kenya.city,
          "addressCountry": seoConfig.contact.address.kenya.country
        },
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": -1.2921,
          "longitude": 36.8219
        },
        "openingHoursSpecification": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"],
          "opens": "08:00",
          "closes": "18:00"
        },
        "priceRange": seoConfig.business.priceRange,
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": seoConfig.business.rating.value,
          "reviewCount": seoConfig.business.rating.count
        }
      };
      
    case 'service':
      return {
        "@context": "https://schema.org",
        "@type": "Service",
        "name": data?.name || "Digital Solutions",
        "description": data?.description || seoConfig.siteDescription,
        "provider": {
          "@type": "Organization",
          "name": seoConfig.business.name,
          "url": baseUrl
        },
        "areaServed": ["US", "KE", "Global"],
        "serviceType": data?.serviceType || seoConfig.business.services
      };
      
    default:
      return null;
  }
};