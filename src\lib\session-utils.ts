// src/lib/session-utils.ts
import { getSession } from "next-auth/react";

/**
 * Force refresh the current session to sync with database changes
 * Useful after email verification, role changes, etc.
 */
export async function refreshSession(): Promise<void> {
  try {
    // Trigger a session refresh by calling getSession with force refresh
    await fetch("/api/auth/session?update");
    
    // Force a page reload to ensure all components get the updated session
    if (typeof window !== "undefined") {
      window.location.reload();
    }
  } catch (error) {
    console.error("❌ Error refreshing session:", error);
  }
}

/**
 * Check if user's email is verified and refresh session if needed
 * Returns true if email is verified, false otherwise
 */
export async function checkAndRefreshEmailVerification(userId: string): Promise<boolean> {
  try {
    // Check current database state
    const response = await fetch(`/api/users/${userId}/verification-status`);
    const data = await response.json();
    
    if (data.emailVerified) {
      // If email is verified in database, refresh the session
      await refreshSession();
      return true;
    }
    
    return false;
  } catch (error) {
    console.error("❌ Error checking email verification status:", error);
    return false;
  }
}

/**
 * Modern session update utility using NextAuth's built-in update method
 * This is the preferred method for updating session data
 */
export async function updateSessionData(updates: Record<string, any>): Promise<void> {
  try {
    // Use NextAuth's update method to trigger JWT callback refresh
    const { update } = await import("next-auth/react");
    await update(updates);
  } catch (error) {
    console.error("❌ Error updating session data:", error);
  }
}
